/* Tablet Styles */
@media (max-width: 1024px) {
    .nav-container {
        padding: 0 20px;
    }

    .hero-title {
        font-size: 48px;
    }

    .hero-subtitle {
        font-size: 24px;
    }

    .grid-container {
        padding: 0 20px;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }

    .product-card.large {
        grid-column: span 1;
    }

    .footer-container {
        padding: 0 20px;
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }
}

/* Mobile Styles */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: rgba(251, 251, 253, 0.95);
        backdrop-filter: saturate(180%) blur(20px);
        flex-direction: column;
        padding: 20px;
        gap: 16px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }

    .nav-menu.active {
        display: flex;
    }

    .menu-toggle {
        display: flex;
    }

    .search-icon,
    .bag-icon {
        display: none;
    }

    .hero {
        padding: 100px 0 60px;
    }

    .hero-title {
        font-size: 36px;
        line-height: 1.1;
    }

    .hero-subtitle {
        font-size: 20px;
        line-height: 1.2;
    }

    .hero-cta {
        flex-direction: column;
        align-items: center;
        gap: 16px;
    }

    .btn-primary,
    .btn-secondary {
        width: 200px;
        text-align: center;
    }

    .phone-mockup {
        width: 250px;
        height: 500px;
    }

    .grid-container {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .product-card {
        min-height: 350px;
    }

    .card-content {
        padding: 30px 20px 15px;
    }

    .card-content h2 {
        font-size: 32px;
    }

    .card-content h3 {
        font-size: 24px;
    }

    .card-content p {
        font-size: 17px;
    }

    .card-cta {
        flex-direction: column;
        gap: 12px;
    }

    .services-container {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .tv-shows-container {
        padding: 0 20px;
    }

    .shows-grid {
        grid-template-columns: 1fr;
        grid-template-rows: auto;
        gap: 20px;
    }

    .show-card.featured {
        grid-row: span 1;
    }

    .show-image,
    .show-card.featured .show-image {
        height: 200px;
    }

    .section-header h2 {
        font-size: 36px;
    }

    .section-header p {
        font-size: 18px;
    }

    .service-card {
        padding: 30px 15px;
    }

    .footer-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
    }

    .footer-bottom {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .footer-links {
        justify-content: center;
        gap: 15px;
    }
}

/* Small Mobile Styles */
@media (max-width: 480px) {
    .nav-container {
        padding: 0 16px;
    }

    .hero {
        padding: 80px 0 40px;
    }

    .hero-content {
        padding: 0 16px;
    }

    .hero-title {
        font-size: 28px;
    }

    .hero-subtitle {
        font-size: 18px;
    }

    .phone-mockup {
        width: 200px;
        height: 400px;
    }

    .grid-container {
        padding: 0 16px;
    }

    .product-card {
        min-height: 300px;
    }

    .card-content {
        padding: 25px 15px 10px;
    }

    .card-content h2 {
        font-size: 28px;
    }

    .card-content h3 {
        font-size: 20px;
    }

    .card-content p {
        font-size: 15px;
    }

    .services-container {
        padding: 0 16px;
    }

    .service-card {
        padding: 25px 10px;
    }

    .service-card h3 {
        font-size: 20px;
    }

    .service-card p {
        font-size: 15px;
    }

    .tv-shows-container {
        padding: 0 16px;
    }

    .section-header h2 {
        font-size: 28px;
    }

    .section-header p {
        font-size: 16px;
    }

    .show-info {
        padding: 15px;
    }

    .show-info h3 {
        font-size: 20px;
    }

    .show-info h4 {
        font-size: 16px;
    }

    .show-info p {
        font-size: 13px;
    }

    .tv-cta .btn-primary {
        padding: 14px 28px;
        font-size: 16px;
    }

    /* Lock Screen Responsive */
    .lock-screen {
        padding: 15px 10px;
    }

    .time-large {
        font-size: 48px;
    }

    .status-bar {
        font-size: 12px;
        margin-bottom: 30px;
    }

    .notification {
        padding: 10px;
    }

    .control-button {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }

    .footer-container {
        padding: 0 16px;
        grid-template-columns: 1fr;
        gap: 25px;
    }

    .footer-bottom {
        padding: 15px 16px 0;
    }

    .footer-links {
        flex-direction: column;
        gap: 10px;
    }
}

/* Landscape Mobile */
@media (max-width: 768px) and (orientation: landscape) {
    .hero {
        padding: 80px 0 40px;
    }

    .hero-title {
        font-size: 32px;
    }

    .hero-subtitle {
        font-size: 18px;
    }

    .phone-mockup {
        width: 180px;
        height: 360px;
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .phone-mockup,
    .ipad-mockup,
    .macbook-mockup,
    .watch-mockup,
    .vision-mockup,
    .airpods-mockup,
    .appletv-mockup {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .phone-mockup {
        animation: none;
    }

    .scroll-reveal {
        opacity: 1;
        transform: none;
    }
}