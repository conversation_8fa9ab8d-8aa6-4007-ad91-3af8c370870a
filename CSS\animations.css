/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Enhanced Keyframe Animations */
@keyframes dynamicIslandPulse {
    0%, 100% {
        transform: scale(1);
        background: rgba(0, 0, 0, 0.8);
    }
    50% {
        transform: scale(1.05);
        background: rgba(0, 0, 0, 0.9);
    }
}

@keyframes phoneScreenGlow {
    0%, 100% {
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    }
    50% {
        box-shadow: 0 20px 60px rgba(0, 122, 255, 0.2), 0 0 30px rgba(0, 122, 255, 0.1);
    }
}

@keyframes lockScreenSlideUp {
    0% {
        opacity: 0;
        transform: translateY(100%) scale(0.8);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes notificationSlide {
    0% {
        opacity: 0;
        transform: translateX(-100%);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes appleLogoSpin {
    0% {
        transform: rotateY(0deg);
    }
    100% {
        transform: rotateY(360deg);
    }
}

@keyframes productCardFloat {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

@keyframes textGlow {
    0%, 100% {
        text-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
    }
    50% {
        text-shadow: 0 0 20px rgba(255, 255, 255, 0.6), 0 0 30px rgba(255, 255, 255, 0.4);
    }
}

@keyframes ripple {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(4);
        opacity: 0;
    }
}

@keyframes slideInFromBottom {
    0% {
        opacity: 0;
        transform: translateY(100px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInFromTop {
    0% {
        opacity: 0;
        transform: translateY(-100px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes elasticIn {
    0% {
        opacity: 0;
        transform: scale(0) rotate(-360deg);
    }
    50% {
        opacity: 1;
        transform: scale(1.2) rotate(-180deg);
    }
    100% {
        opacity: 1;
        transform: scale(1) rotate(0deg);
    }
}

/* Fade In Animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Hero Animations */
.hero-title {
    animation: slideInFromTop 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.2s both;
}

.hero-subtitle {
    animation: slideInFromBottom 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.4s both;
}

.hero-cta {
    animation: bounceIn 1s ease-out 0.6s both;
}

.hero-image {
    animation: elasticIn 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.8s both;
}

/* Enhanced Phone Animation */
.phone-mockup {
    animation: float 6s ease-in-out infinite, phoneScreenGlow 4s ease-in-out infinite;
}

.phone-mockup.active {
    animation: float 6s ease-in-out infinite, phoneScreenGlow 2s ease-in-out infinite;
}

/* Lock Screen Animations */
.lock-screen.active {
    animation: lockScreenSlideUp 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.notification {
    animation: notificationSlide 0.6s ease-out 1s both;
}

/* Apple Logo Animation */
.nav-logo svg {
    transition: all 0.3s ease;
}

.nav-logo:hover svg {
    animation: appleLogoSpin 1s ease-in-out;
}

/* Product Card Animations */
.product-card {
    animation: slideInFromBottom 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

.product-card:nth-child(1) {
    animation-delay: 0.1s;
    animation-name: slideInFromBottom, productCardFloat;
    animation-duration: 0.8s, 4s;
    animation-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94), ease-in-out;
    animation-iteration-count: 1, infinite;
    animation-delay: 0.1s, 1s;
}
.product-card:nth-child(2) { animation-delay: 0.2s; }
.product-card:nth-child(3) { animation-delay: 0.3s; }
.product-card:nth-child(4) { animation-delay: 0.4s; }
.product-card:nth-child(5) { animation-delay: 0.5s; }
.product-card:nth-child(6) { animation-delay: 0.6s; }

/* TV Shows Animations */
.tv-shows-section {
    background: linear-gradient(-45deg, #000000, #1a1a1a, #000000, #2a2a2a);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
}

.section-header h2 {
    animation: textGlow 3s ease-in-out infinite;
}

.show-card {
    animation: fadeIn 0.8s ease-out both;
}

.show-card:nth-child(1) { animation-delay: 0.1s; }
.show-card:nth-child(2) { animation-delay: 0.2s; }
.show-card:nth-child(3) { animation-delay: 0.3s; }
.show-card:nth-child(4) { animation-delay: 0.4s; }
.show-card:nth-child(5) { animation-delay: 0.5s; }
.show-card:nth-child(6) { animation-delay: 0.6s; }

.show-card:hover {
    animation: productCardFloat 2s ease-in-out infinite;
}

/* Service Card Animations */
.service-card {
    animation: fadeIn 0.8s ease-out both;
}

.service-card:nth-child(1) { animation-delay: 0.2s; }
.service-card:nth-child(2) { animation-delay: 0.4s; }
.service-card:nth-child(3) { animation-delay: 0.6s; }

/* Hover Animations */
.product-card {
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.product-card:hover .card-image {
    transform: scale(1.05);
    transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Button Hover Effects */
.btn-primary,
.btn-secondary {
    position: relative;
    overflow: hidden;
}

.btn-primary::before,
.btn-secondary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before,
.btn-secondary:hover::before {
    left: 100%;
}

/* Navigation Animation */
.nav-link {
    position: relative;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    width: 0;
    height: 1px;
    background: #0071e3;
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-link:hover::after {
    width: 100%;
}

/* Phone Mockup Animation */
.phone-mockup {
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

/* Loading Animation for Images */
.card-image {
    position: relative;
    overflow: hidden;
}

.card-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Scroll Reveal Animation */
.scroll-reveal {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.8s ease-out;
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

/* Menu Toggle Animation */
.menu-toggle.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.menu-toggle.active span:nth-child(2) {
    opacity: 0;
}

.menu-toggle.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

/* Smooth Scroll Indicator */
.scroll-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 3px;
    background: linear-gradient(90deg, #007aff, #0051d5);
    z-index: 9999;
    transition: width 0.1s ease-out;
}

/* Enhanced Hover Effects */
.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.product-card:hover::before {
    opacity: 1;
}

/* TV Show Card Enhanced Animations */
.show-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.show-card:hover::after {
    transform: translateX(100%);
}

/* Animate on Scroll Class */
.animate-in {
    animation-play-state: running !important;
}

/* Performance Optimizations */
.product-card,
.show-card,
.phone-mockup {
    will-change: transform;
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Accessibility - Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .product-card,
    .show-card,
    .phone-mockup,
    .hero-title,
    .hero-subtitle,
    .hero-cta,
    .hero-image {
        animation: none !important;
        transition: none !important;
    }

    .lock-screen.active {
        opacity: 1 !important;
        transform: none !important;
    }
}