/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #1d1d1f;
    background-color: #ffffff;
    overflow-x: hidden;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(251, 251, 253, 0.8);
    backdrop-filter: saturate(180%) blur(20px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    z-index: 1000;
    padding: 0;
}

.nav-container {
    max-width: 1024px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 22px;
    height: 44px;
}

.nav-logo svg {
    color: #1d1d1f;
    width: 16px;
    height: 20px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 32px;
    margin: 0;
}

.nav-link {
    color: #1d1d1f;
    text-decoration: none;
    font-size: 12px;
    font-weight: 400;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.nav-link:hover {
    opacity: 1;
}

.nav-icons {
    display: flex;
    align-items: center;
    gap: 18px;
}

.search-icon,
.bag-icon {
    cursor: pointer;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.search-icon:hover,
.bag-icon:hover {
    opacity: 1;
}

.search-icon svg,
.bag-icon svg {
    color: #1d1d1f;
}

.menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 3px;
}

.menu-toggle span {
    width: 18px;
    height: 1px;
    background: #1d1d1f;
    transition: all 0.3s ease;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 120px 0 80px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero-content {
    max-width: 1024px;
    margin: 0 auto;
    padding: 0 22px;
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 56px;
    font-weight: 600;
    line-height: 1.07;
    letter-spacing: -0.005em;
    margin-bottom: 8px;
}

.hero-subtitle {
    font-size: 28px;
    font-weight: 400;
    line-height: 1.14;
    letter-spacing: 0.007em;
    margin-bottom: 24px;
    opacity: 0.9;
}

.hero-cta {
    display: flex;
    gap: 24px;
    justify-content: center;
    margin-bottom: 40px;
}

.btn-primary,
.btn-secondary {
    padding: 12px 24px;
    border-radius: 980px;
    text-decoration: none;
    font-size: 17px;
    font-weight: 400;
    transition: all 0.3s ease;
    display: inline-block;
}

.btn-primary {
    background: #0071e3;
    color: white;
    border: 1px solid #0071e3;
}

.btn-primary:hover {
    background: #0077ed;
    border-color: #0077ed;
}

.btn-secondary {
    background: transparent;
    color: #0071e3;
    border: 1px solid #0071e3;
}

.btn-secondary:hover {
    background: #0071e3;
    color: white;
}

.hero-image {
    position: relative;
    margin-top: 40px;
}

.phone-mockup {
    width: 300px;
    height: 600px;
    margin: 0 auto;
    background: linear-gradient(145deg, #2c2c2e, #1c1c1e);
    border-radius: 40px;
    padding: 8px;
    position: relative;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.phone-screen {
    width: 100%;
    height: 100%;
    background: linear-gradient(145deg, #000, #1a1a1a);
    border-radius: 32px;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
    overflow: hidden;
}

.phone-screen:hover {
    transform: scale(1.02);
}

.phone-notch {
    position: absolute;
    top: 8px;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    height: 30px;
    background: #000;
    border-radius: 15px;
    z-index: 10;
}

/* iOS 26 Lock Screen */
.lock-screen {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    border-radius: 32px;
    padding: 20px 15px;
    color: white;
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.lock-screen.active {
    opacity: 1;
    transform: scale(1);
}

.lock-screen.hidden {
    display: block;
}

.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 40px;
    padding-top: 25px;
}

.status-icons {
    display: flex;
    gap: 5px;
    font-size: 12px;
}

.lock-screen-content {
    height: calc(100% - 80px);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
}

.date-time {
    text-align: center;
    margin-bottom: 30px;
}

.date {
    font-size: 16px;
    font-weight: 500;
    opacity: 0.8;
    margin-bottom: 5px;
}

.time-large {
    font-size: 64px;
    font-weight: 200;
    line-height: 1;
    letter-spacing: -2px;
}

.dynamic-island {
    width: 120px;
    height: 32px;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 16px;
    margin: 20px 0;
    backdrop-filter: blur(20px);
    animation: dynamicIslandPulse 3s ease-in-out infinite;
}

.notifications {
    width: 100%;
    margin: 20px 0;
}

.notification {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    padding: 12px;
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
}

.app-icon {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}

.notification-content {
    flex: 1;
}

.app-name {
    font-size: 12px;
    font-weight: 600;
    opacity: 0.8;
}

.notification-text {
    font-size: 14px;
    font-weight: 400;
}

.lock-screen-controls {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 0 20px;
    margin-bottom: 20px;
}

.control-button {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.control-button:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.home-indicator {
    width: 134px;
    height: 5px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    margin-bottom: 8px;
}

/* Product Grid */
.product-grid {
    padding: 80px 0;
    background: #f5f5f7;
}

.grid-container {
    max-width: 1024px;
    margin: 0 auto;
    padding: 0 22px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
}

.product-card {
    background: white;
    border-radius: 18px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    min-height: 400px;
    display: flex;
    flex-direction: column;
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.product-card.large {
    grid-column: span 2;
    min-height: 500px;
}

.card-content {
    padding: 40px 40px 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
}

.card-content h2 {
    font-size: 40px;
    font-weight: 600;
    line-height: 1.1;
    letter-spacing: -0.003em;
    margin-bottom: 8px;
    color: #1d1d1f;
}

.card-content h3 {
    font-size: 28px;
    font-weight: 600;
    line-height: 1.14;
    letter-spacing: 0.007em;
    margin-bottom: 8px;
    color: #1d1d1f;
}

.card-content p {
    font-size: 19px;
    font-weight: 400;
    line-height: 1.21;
    letter-spacing: 0.012em;
    color: #86868b;
    margin-bottom: 20px;
}

.card-cta {
    display: flex;
    gap: 24px;
    justify-content: center;
}

.btn-link {
    color: #0071e3;
    text-decoration: none;
    font-size: 17px;
    font-weight: 400;
    transition: color 0.3s ease;
}

.btn-link:hover {
    color: #0077ed;
    text-decoration: underline;
}

.card-image {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    position: relative;
}

/* Product Mockups */
.ipad-mockup {
    width: 200px;
    height: 280px;
    background: linear-gradient(145deg, #e8e8ed, #d1d1d6);
    border-radius: 20px;
    position: relative;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.ipad-mockup::before {
    content: '';
    position: absolute;
    top: 15px;
    left: 15px;
    right: 15px;
    bottom: 15px;
    background: #000;
    border-radius: 12px;
}

.macbook-mockup {
    width: 220px;
    height: 140px;
    background: linear-gradient(145deg, #e8e8ed, #d1d1d6);
    border-radius: 8px;
    position: relative;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.macbook-mockup::before {
    content: '';
    position: absolute;
    top: 8px;
    left: 8px;
    right: 8px;
    bottom: 20px;
    background: #000;
    border-radius: 4px;
}

.macbook-mockup::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 240px;
    height: 8px;
    background: linear-gradient(145deg, #d1d1d6, #c7c7cc);
    border-radius: 0 0 12px 12px;
}

.watch-mockup {
    width: 120px;
    height: 140px;
    background: linear-gradient(145deg, #2c2c2e, #1c1c1e);
    border-radius: 30px;
    position: relative;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.watch-mockup::before {
    content: '';
    position: absolute;
    top: 15px;
    left: 15px;
    right: 15px;
    bottom: 15px;
    background: #000;
    border-radius: 20px;
}

.vision-mockup {
    width: 180px;
    height: 100px;
    background: linear-gradient(145deg, #f0f0f0, #e0e0e0);
    border-radius: 40px;
    position: relative;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.vision-mockup::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 30px;
    width: 40px;
    height: 40px;
    background: #000;
    border-radius: 50%;
}

.vision-mockup::after {
    content: '';
    position: absolute;
    top: 20px;
    right: 30px;
    width: 40px;
    height: 40px;
    background: #000;
    border-radius: 50%;
}

.airpods-mockup {
    width: 100px;
    height: 120px;
    background: linear-gradient(145deg, #ffffff, #f0f0f0);
    border-radius: 50px;
    position: relative;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.airpods-mockup::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 20px;
    width: 20px;
    height: 60px;
    background: #ffffff;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.airpods-mockup::after {
    content: '';
    position: absolute;
    top: 20px;
    right: 20px;
    width: 20px;
    height: 60px;
    background: #ffffff;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.appletv-mockup {
    width: 120px;
    height: 30px;
    background: linear-gradient(145deg, #2c2c2e, #1c1c1e);
    border-radius: 8px;
    position: relative;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* Apple TV+ Shows Section */
.tv-shows-section {
    background: #000;
    color: white;
    padding: 100px 0;
    position: relative;
    overflow: hidden;
}

.tv-shows-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(20, 20, 20, 0.9) 100%);
    z-index: 1;
}

.tv-shows-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 22px;
    position: relative;
    z-index: 2;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-header h2 {
    font-size: 48px;
    font-weight: 600;
    line-height: 1.1;
    margin-bottom: 16px;
    background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-header p {
    font-size: 21px;
    font-weight: 400;
    opacity: 0.8;
    max-width: 600px;
    margin: 0 auto;
}

.shows-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    grid-template-rows: repeat(2, 1fr);
    gap: 24px;
    margin-bottom: 60px;
}

.show-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    cursor: pointer;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.show-card:hover {
    transform: translateY(-8px) scale(1.02);
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.show-card.featured {
    grid-row: span 2;
}

.show-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.show-card.featured .show-image {
    height: 300px;
}

.show-poster {
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    transition: transform 0.4s ease;
}

.show-card:hover .show-poster {
    transform: scale(1.1);
}

.severance {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
}

.foundation {
    background: linear-gradient(135deg, #2c1810 0%, #8b4513 50%, #daa520 100%);
}

.ted-lasso {
    background: linear-gradient(135deg, #228b22 0%, #32cd32 50%, #90ee90 100%);
}

.morning-show {
    background: linear-gradient(135deg, #191970 0%, #4169e1 50%, #87ceeb 100%);
}

.mythic-quest {
    background: linear-gradient(135deg, #800080 0%, #9932cc 50%, #dda0dd 100%);
}

.defending-jacob {
    background: linear-gradient(135deg, #2f4f4f 0%, #708090 50%, #b0c4de 100%);
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: #000;
    opacity: 0;
    transition: all 0.3s ease;
    backdrop-filter: blur(20px);
}

.show-card:hover .play-button {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.1);
}

.show-info {
    padding: 20px;
}

.show-info h3 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 8px;
    line-height: 1.2;
}

.show-info h4 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 6px;
    line-height: 1.2;
}

.show-info p {
    font-size: 14px;
    font-weight: 400;
    opacity: 0.8;
    line-height: 1.4;
    margin-bottom: 12px;
}

.show-meta {
    display: flex;
    gap: 12px;
}

.genre,
.rating {
    font-size: 12px;
    font-weight: 500;
    padding: 4px 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    backdrop-filter: blur(10px);
}

.tv-cta {
    text-align: center;
}

.tv-cta .btn-primary {
    background: linear-gradient(135deg, #007aff 0%, #0051d5 100%);
    border: none;
    padding: 16px 32px;
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 16px;
    box-shadow: 0 8px 24px rgba(0, 122, 255, 0.3);
}

.tv-cta .btn-primary:hover {
    background: linear-gradient(135deg, #0051d5 0%, #007aff 100%);
    transform: translateY(-2px);
    box-shadow: 0 12px 32px rgba(0, 122, 255, 0.4);
}

.trial-info {
    font-size: 14px;
    opacity: 0.7;
    margin: 0;
}

/* Services Section */
.services {
    padding: 80px 0;
    background: #ffffff;
}

.services-container {
    max-width: 1024px;
    margin: 0 auto;
    padding: 0 22px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
}

.service-card {
    text-align: center;
    padding: 40px 20px;
}

.service-icon {
    margin-bottom: 20px;
    color: #0071e3;
}

.service-card h3 {
    font-size: 24px;
    font-weight: 600;
    line-height: 1.17;
    letter-spacing: 0.009em;
    margin-bottom: 8px;
    color: #1d1d1f;
}

.service-card p {
    font-size: 17px;
    font-weight: 400;
    line-height: 1.24;
    letter-spacing: -0.022em;
    color: #86868b;
    margin-bottom: 16px;
}

.service-link {
    color: #0071e3;
    text-decoration: none;
    font-size: 17px;
    font-weight: 400;
    transition: color 0.3s ease;
}

.service-link:hover {
    color: #0077ed;
    text-decoration: underline;
}

/* Footer */
.footer {
    background: #f5f5f7;
    padding: 40px 0 20px;
    border-top: 1px solid #d2d2d7;
}

.footer-container {
    max-width: 1024px;
    margin: 0 auto;
    padding: 0 22px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-section h4 {
    font-size: 12px;
    font-weight: 600;
    line-height: 1.33;
    letter-spacing: -0.01em;
    color: #1d1d1f;
    margin-bottom: 16px;
    text-transform: uppercase;
}

.footer-section ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.footer-section li {
    margin-bottom: 8px;
}

.footer-section a {
    color: #424245;
    text-decoration: none;
    font-size: 12px;
    font-weight: 400;
    line-height: 1.33;
    letter-spacing: -0.01em;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: #0071e3;
    text-decoration: underline;
}

.footer-bottom {
    max-width: 1024px;
    margin: 0 auto;
    padding: 20px 22px 0;
    border-top: 1px solid #d2d2d7;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 20px;
}

.footer-legal p {
    font-size: 12px;
    font-weight: 400;
    line-height: 1.33;
    letter-spacing: -0.01em;
    color: #86868b;
    margin-bottom: 8px;
}

.footer-legal a {
    color: #424245;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-legal a:hover {
    color: #0071e3;
    text-decoration: underline;
}

.footer-links {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    align-items: center;
}

.footer-links span {
    font-size: 12px;
    font-weight: 400;
    line-height: 1.33;
    letter-spacing: -0.01em;
    color: #86868b;
}

.footer-links a {
    font-size: 12px;
    font-weight: 400;
    line-height: 1.33;
    letter-spacing: -0.01em;
    color: #424245;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #0071e3;
    text-decoration: underline;
}

.footer-country {
    font-size: 12px;
    font-weight: 400;
    line-height: 1.33;
    letter-spacing: -0.01em;
    color: #86868b;
}

/* Search Overlay */
.search-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(20px);
    z-index: 2000;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    padding-top: 100px;
    animation: fadeIn 0.3s ease-out;
}

.search-container {
    background: white;
    border-radius: 12px;
    padding: 20px;
    width: 90%;
    max-width: 600px;
    display: flex;
    align-items: center;
    gap: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.search-input {
    flex: 1;
    border: none;
    outline: none;
    font-size: 18px;
    font-family: inherit;
    padding: 10px 0;
    background: transparent;
}

.search-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #86868b;
    padding: 5px;
    transition: color 0.3s ease;
}

.search-close:hover {
    color: #1d1d1f;
}